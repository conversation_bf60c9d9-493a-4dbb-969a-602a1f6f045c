<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>销售提单-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__STATIC__/zad_js/plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="__STATIC__/zad_js/css/style.css">
    <link rel="icon" type="image/png" href="__STATIC__/zad_js/picture/logo.png">
    <style>
        [v-cloak] {
            display: none;
        }

        /* 字体设置 */
        @font-face {
            font-family: "zad";
            /*src: url("../font/SarasaUiSC-SemiBold.ttf");*/
            src: url("__STATIC__/zad_js/font/MiSans-Medium.woff2");
        }

        /* 字体应用组件 */
        html,
        body,
        button,
        input,
        select,
        textarea,
        form {
            font-family: "zad", sans-serif !important;
        }

        .el-table__body-wrapper {
            z-index: 2;
        }

        .el-table__fixed {
            height: 100% !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
        }

        .wide-notification {
            width: 300px !important;
            /* 设置通知宽度为300px */
        }

        /* 如果需要调整消息文本的样式 */
        .wide-notification .el-notification__content {
            text-align: left;
            word-break: break-all;
            margin: 5px 0;
        }
    </style>
</head>

<body class="hold-transition">
    <div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
        <!--标题-->
        <div class="content-header">
            <h1>销售提单</h1>
        </div>
        <!--内容-->
        <div class="app-container">
            <div class="box">
                <br>
                <!--提单查询-->
                <div>
                    <el-row :gutter="20">
                        <!--提单标志-->
                        <el-col :span="4">
                            <el-select size="mini" v-model="deliveryNoteQuery.deliveryNoteFlagList" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择提单标志"
                                :style="{width: '100%'}" value="" @visible-change="getListDeliveryNoteFlagLabelList"
                                @change="getDeliveryNoteList()" clearable>
                                <el-option v-for="item in list_deliveryNoteFlagLabelList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--审核标志-->
                        <el-col :span="4">
                            <el-select size="mini" v-model="deliveryNoteQuery.auditFlagList" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择审核标志"
                                :style="{width: '100%'}" value="" @visible-change="getListAuditFlagLabelList"
                                @change="getDeliveryNoteList()" clearable>
                                <el-option v-for="item in list_auditFlagLabelList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--提单编号-->
                        <el-col :span="4">
                            <el-input size="mini" placeholder="请输入提单编号" clearable :style="{width: '100%'}"
                                v-model.trim="deliveryNoteQuery.deliveryNoteNumber" @change="getDeliveryNoteList()"
                                prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--操作按钮-->
                        <el-col :span="12">
                            <el-tooltip content="查询销售提单" placement="top">
                                <el-button size="mini" @click="getDeliveryNoteList()" type="primary"
                                    icon="el-icon-search" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="清空查询条件" placement="top">
                                <el-button size="mini" @click="refreshListSearch()" type="primary"
                                    icon="el-icon-refresh" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="显示提单详情" placement="top">
                                <el-button size="mini" @click="switchListDeliveryNoteDetails()" type="primary"
                                    icon="el-icon-document" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="创建新提单" placement="top">
                                <el-button size="mini" @click="details_openDeliveryNoteDetailsDialog(true, null)"
                                    type="success" icon="el-icon-edit-outline" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="参照订单创建提单" placement="top">
                                <el-button size="mini" @click="order_openOrderListDialog(true)" type="success"
                                    icon="el-icon-document-copy" circle>
                                </el-button>
                            </el-tooltip>
                            <!--<el-tooltip content="提交提单" placement="top">
                            <el-button type="success" size="mini"
                                       @click="list_submitDeliveryNote(null)"
                                       icon="el-icon-upload2" circle></el-button>
                        </el-tooltip>-->
                            <el-tooltip content="撤回提单" placement="top">
                                <el-button type="warning" size="mini" @click="list_undoSubmitDeliveryNote(null)"
                                    icon="el-icon-refresh-left" circle></el-button>
                            </el-tooltip>
                            <el-tooltip content="删除提单" placement="top">
                                <el-button type="danger" size="mini" @click="list_deleteDeliveryNote(null)"
                                    icon="el-icon-delete-solid" circle></el-button>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                    <br>
                    <el-row :gutter="20">
                        <!--客户名称-->
                        <el-col :span="4">
                            <el-select size="mini" v-model="deliveryNoteQuery.customerNumberList" filterable remote
                                multiple collapse-tags default-first-option :reserve-keyword="true"
                                placeholder="请选择客户名称" :style="{width: '100%'}" value=""
                                :remote-method="getListCustomerLabelList" @change="getDeliveryNoteList()" clearable>
                                <el-option v-for="item in list_customerLabelList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--业务部门-->
                        <el-col :span="4">
                            <el-select size="mini" v-model="deliveryNoteQuery.departmentNumberList" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择业务部门"
                                :style="{width: '100%'}" value="" @visible-change="getListDepartmentLabelList"
                                @change="getDeliveryNoteList()" clearable>
                                <el-option v-for="item in list_departmentLabelList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--业务员-->
                        <el-col :span="4">
                            <el-select size="mini" v-model="deliveryNoteQuery.employeeNumberList" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择业务员"
                                :style="{width: '100%'}" value="" @visible-change="getListEmployeeLabelList"
                                @change="getDeliveryNoteList()" clearable>
                                <el-option v-for="item in list_employeeLabelList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--业务日期-->
                        <el-col :span="6">
                            <el-date-picker size="mini" placement="bottom-start" unlink-panels
                                v-model="deliveryNoteQuery.queryDate" type="daterange" start-placeholder="开始日期"
                                end-placeholder="结束日期" value-format="yyyy-MM-dd" :style="{width: '100%'}"
                                @change="getDeliveryNoteList()" clearable>
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </div>
                <br>
                <!--提单列表-->
                <div>
                    <!--提单表格-->
                    <el-table size="small" v-loading="list_tableLoading" :row-style="{height: '40px'}"
                        :cell-style="{padding: '0px'}" :data="deliveryNoteList" @row-click="showListDeliveryNoteDetails"
                        @selection-change="list_handleSelectionChange" highlight-current-row
                        :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                        <el-table-column fixed type="selection" :selectable="list_selectable" width="60">
                        </el-table-column>
                        <el-table-column fixed type="index" align="center" width="60">
                        </el-table-column>
                        <el-table-column prop="XSTD_TDBZ" label="提单标志" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_YWRQ" label="业务日期" align="center" width="100" sortable
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.XSTD_YWRQ) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="XSTD_PJLX" label="单据类型" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="KCBMZD_BMMC" label="部门名称" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SHDKHMC" label="售达客户" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_FKKHMC" label="付款客户" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SODKHMC" label="送达客户" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SPKHMC" label="收票客户" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSYWLX_YWMC" label="业务类型" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="ZWZGZD_ZGXM" label="业务员" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SFQR" label="单据状态" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="LSWBZD_BM" label="币种" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_HL" label="汇率" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSYSFS_FSMC" label="运输方式" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_DHDD" label="到货地点" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_QCBZ" label="期初标志" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_DYPERSON" label="打印人" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_DYDATE" label="打印日期" align="center" width="100" sortable
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.XSTD_YWRQ) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="XSTD_BZ" label="备注" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_C1" label="承运人" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_C2" label="客户合同号" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_C3" label="提运单号" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_C4" label="里程数" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_ADDR" label="客户地址" align="center" width="200" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_TELE" label="电话" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SHBZ" label="审核标志" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_SHXM" label="审核人" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTD_JZBZ" label="记账标志" align="center" width="100" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" width="220" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-tooltip v-if="scope.row.XSTD_SHBZ !== '未提交'" content="查看提单详情" placement="top">
                                    <el-button type="primary" size="mini"
                                        @click="details_openDeliveryNoteDetailsDialog(false, scope.row)"
                                        icon="el-icon-search" circle></el-button>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.XSTD_SHBZ === '未提交'" content="编辑提单" placement="top">
                                    <el-button type="primary" size="mini"
                                        @click="details_openDeliveryNoteDetailsDialog(false, scope.row)"
                                        icon="el-icon-edit" circle></el-button>
                                </el-tooltip>
                                <!--<el-tooltip v-if="scope.row.XSTD_SHBZ === '未提交'" content="提交提单" placement="top">
                                <el-button type="success" size="mini"
                                           @click="list_submitDeliveryNote(scope.row)"
                                           icon="el-icon-upload2" circle></el-button>
                            </el-tooltip>-->
                                <el-tooltip v-if="scope.row.XSTD_SHBZ !== '未提交' && scope.row.XSTD_JZBZ === '未记账'"
                                    content="撤回提单" placement="top">
                                    <el-button type="warning" size="mini"
                                        @click="list_undoSubmitDeliveryNote(scope.row)" icon="el-icon-refresh-left"
                                        circle></el-button>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.XSTD_JZBZ === '未记账'" content="删除提单" placement="top">
                                    <el-button type="danger" size="mini" @click="list_deleteDeliveryNote(scope.row)"
                                        icon="el-icon-delete-solid" circle></el-button>
                                </el-tooltip>
                                <el-tooltip content="打印提单" placement="top">
                                    <el-button type="success" size="mini" @click="list_printDeliveryNote(scope.row)"
                                        icon="el-icon-printer" circle style="margin-right: 10px;"></el-button>
                                </el-tooltip>
                                <el-dropdown trigger="click"
                                    @command="(command) => list_downloadDeliveryNote(scope.row, command)">
                                    <el-tooltip content="下载提单" placement="top">
                                        <el-button type="success" size="mini" icon="el-icon-download"
                                            circle></el-button>
                                    </el-tooltip>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="excel">Excel格式</el-dropdown-item>
                                        <el-dropdown-item command="pdf">PDF格式</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <el-pagination small style="text-align: right;" layout="prev, pager, next"
                        @current-change="handleListCurrentChange" :current-page="deliveryNoteQuery.currentPage"
                        :page-size="deliveryNoteQuery.pageSize" :total="deliveryNoteQuery.total">
                    </el-pagination>
                    <!--分录列表-->
                    <el-table v-if="list_deliveryNoteDetailsTableVisible" v-loading="list_ladingEntryLoading"
                        size="small" max-height="275" :row-style="{height: '40px'}" :cell-style="{padding: '0px'}"
                        :data="list_ladingEntryList" highlight-current-row
                        :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                        <el-table-column fixed type="index" align="center" width="40">
                        </el-table-column>
                        <el-table-column prop="DF_HT_MX" label="合同明细" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="LSCKZD_CKMC" label="仓库名称" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_WLBH" label="产品编号" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="lswlzd_wlmc" label="产品名称" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="LSWLZD_GGXH" label="规格型号" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_FLLX_MC" label="分录类型" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="xstdmx_kcfs_mc" label="库存方式" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_KCYL_mc" label="库存预留" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_JHRQ" label="交货日期" align="center" width="150" sortable
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.XSTDMX_JHRQ) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_ZSL" label="数量" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="zmc" label="计量单位" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_FSL2" label="辅助数量" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="fmc" label="辅助计量" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_YZHSJ" label="含税单价" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_YZXSJ" label="销售单价" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_YXSE" label="销售金额" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_SL" label="税率(%)" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_YSE" label="税额" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <!--<el-table-column prop="XSTDMX_DBZKBL" label="单笔折扣比例" align="center" width="150" sortable
                                     show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="XSTDMX_ZEZKBL" label="总额折扣比例" align="center" width="150" sortable
                                     show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="XSTDMX_YZKJE" label="折扣金额" align="center" width="150" sortable
                                     show-overflow-tooltip>
                    </el-table-column>-->
                        <el-table-column prop="XSTDMX_DDBH" label="订单编号" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="xstdmx_flly_mc" label="分录来源" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="xstdmx_sfck_mc" label="是否出库" align="center" width="150" sortable
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_LZRQ" label="立账日期" align="center" width="150" sortable
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.XSTDMX_LZRQ) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="XSTDMX_YHKRQ" label="预回款日期" align="center" width="150" sortable
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.XSTDMX_YHKRQ) }}
                            </template>
                        </el-table-column>
                        <!--<el-table-column fixed="right" label="操作" align="center" width="150"
                                     show-overflow-tooltip>
                        <template slot-scope="scope">
                            <el-tooltip content="查看" placement="top">
                                <el-button type="primary" size="mini"
                                           icon="el-icon-search" circle></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>-->
                    </el-table>
                </div>
                <!--提单详情-->
                <div>
                    <el-dialog :visible.sync="details_deliveryNoteDetailsVisible" title="销售提单" width="90%" top="5vh"
                        center>
                        <!--提单详情-->
                        <div>
                            <el-form label-width="90px" size="mini">
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="单据日期">
                                            <el-date-picker size="mini" :style="{width: '100%'}"
                                                v-model="deliveryNoteDetails.XSTD_DJRQ" type="date"
                                                value-format="yyyyMMdd" placeholder="请选择日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="业务日期">
                                            <el-date-picker size="mini" :style="{width: '100%'}"
                                                v-model="deliveryNoteDetails.XSTD_YWRQ" type="date"
                                                value-format="yyyyMMdd" placeholder="请选择日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="提单编号">
                                            <el-input size="mini" placeholder="请输入提单编号" clearable
                                                :style="{width: '100%'}" v-model.trim="deliveryNoteDetails.XSTD_TDBH">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="提单种类">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_ZLBH" filterable
                                                default-first-option placeholder="请选择提单种类" :style="{width: '100%'}"
                                                value="" clearable>
                                                <el-option v-for="item in details_deliveryNoteTypeLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="售达客户">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_SHDKH" filterable
                                                remote default-first-option placeholder="请选择售达客户"
                                                :style="{width: '100%'}" value=""
                                                @change="details_changeSoldToCustomer()"
                                                :remote-method="details_getSoldToCustomerLabelList" clearable>
                                                <el-option v-for="item in details_soldToCustomerLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="送达客户">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_SODKH" filterable
                                                remote default-first-option placeholder="请选择送达客户"
                                                :style="{width: '100%'}" value=""
                                                :remote-method="details_getShippedToCustomerLabelList" clearable>
                                                <el-option v-for="item in details_shippedToCustomerLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="收票客户">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_SPKH" filterable
                                                remote default-first-option placeholder="请选择收票客户"
                                                :style="{width: '100%'}" value=""
                                                :remote-method="details_getInvoiceToCustomerLabelList" clearable>
                                                <el-option v-for="item in details_invoiceToCustomerLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="付款客户">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_FKKH" filterable
                                                remote default-first-option placeholder="请选择付款客户"
                                                :style="{width: '100%'}" value=""
                                                :remote-method="details_getPaymentCustomerLabelList" clearable>
                                                <el-option v-for="item in details_paymentCustomerLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="币种">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_WBBH" filterable
                                                placeholder="请选择币种" :style="{width: '100%'}" value=""
                                                @change="details_getExchangeRate()" clearable>
                                                <el-option v-for="item in details_currencyTypeLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="汇率">
                                            <el-input size="mini" placeholder="请输入汇率"
                                                @change="details_updateExchangeRate()" clearable
                                                :style="{width: '100%'}" v-model.trim="deliveryNoteDetails.XSTD_HL">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="业务部门">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_BMBH" filterable
                                                default-first-option placeholder="请选择业务部门" :style="{width: '100%'}"
                                                value="" @change="details_changeDepartmentSelect()" clearable>
                                                <el-option v-for="item in details_departmentLabelList" :key="item.value"
                                                    :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="业务员">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_RYBH" filterable
                                                default-first-option placeholder="请选择业务员" :style="{width: '100%'}"
                                                value="" @visible-change="details_getEmployeeLabelList" clearable>
                                                <el-option v-for="item in details_employeeLabelList" :key="item.value"
                                                    :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="业务类型">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSYWLX_YWBH" filterable
                                                default-first-option placeholder="请选择业务类型" :style="{width: '100%'}"
                                                value="" clearable>
                                                <el-option v-for="item in details_businessTypeLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <!--<el-col :span="6">
                                    <el-form-item label="折扣政策">
                                        <el-select size="mini"
                                                   v-model="deliveryNoteDetails.XSTD_ZKZC"
                                                   filterable
                                                   default-first-option :reserve-keyword="true"
                                                   placeholder="请选择折扣政策"
                                                   :style="{width: '100%'}" value=""
                                                   clearable>
                                            <el-option
                                                    v-for="item in details_discountPolicyLabelList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>-->
                                    <el-col :span="6">
                                        <el-form-item label="客户电话">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_TELE" filterable
                                                allow-create default-first-option placeholder="请选择客户电话"
                                                :style="{width: '100%'}" value="" clearable>
                                                <el-option v-for="item in details_customerPhoneLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="客户地址">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_ADDR" filterable
                                                allow-create default-first-option placeholder="请选择客户地址"
                                                :style="{width: '100%'}" value="" clearable>
                                                <el-option v-for="item in details_customerAddressLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="到货地点">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_DHDD" filterable
                                                allow-create default-first-option placeholder="请选择到货地点"
                                                :style="{width: '100%'}" value="" clearable>
                                                <el-option v-for="item in details_destinationLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="联系人">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_LXR" filterable
                                                allow-create default-first-option placeholder="请选择联系人"
                                                :style="{width: '100%'}" value="" clearable>
                                                <el-option v-for="item in details_contactLabelList" :key="item.value"
                                                    :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="运输方式">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSYSFS_FSBH" filterable
                                                default-first-option placeholder="请选择运输方式" :style="{width: '100%'}"
                                                value="" clearable>
                                                <el-option v-for="item in details_transportationLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="备注">
                                            <el-input size="mini" placeholder="请输入备注" clearable :style="{width: '100%'}"
                                                v-model="deliveryNoteDetails.XSTD_BZ">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="承运人">
                                            <el-input size="mini" placeholder="请输入承运人" clearable
                                                :style="{width: '100%'}" v-model.trim="deliveryNoteDetails.XSTD_C1">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="客户合同号">
                                            <el-select size="mini" v-model="deliveryNoteDetails.XSTD_C2" filterable
                                                allow-create default-first-option placeholder="请选择客户合同号"
                                                :style="{width: '100%'}" value=""
                                                @visible-change="details_getCustomerContractNumberLabelList" clearable>
                                                <el-option v-for="item in details_customerContractNumberLabelList"
                                                    :key="item.value" :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="提运单号">
                                            <el-input size="mini" placeholder="请输入提运单号" clearable
                                                :style="{width: '100%'}" v-model.trim="deliveryNoteDetails.XSTD_C3">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="里程数">
                                            <el-input size="mini" placeholder="请输入里程数" clearable
                                                :style="{width: '100%'}" v-model.trim="deliveryNoteDetails.XSTD_C4">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <br>
                        <!--提单分录-->
                        <div>
                            <el-table size="mini" max-height="230" :data="deliveryNoteDetails.ladingEntryList"
                                highlight-current-row :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                border>
                                <el-table-column fixed type="index" align="center" width="40">
                                </el-table-column>
                                <el-table-column label="合同明细" prop="DF_HT_MX" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="仓库名称" align="center" width="159" sortable>
                                    <template slot-scope="scope">
                                        <el-select size="mini" v-model="scope.row.XSTDMX_CKBH" filterable
                                            default-first-option placeholder="请选择仓库名称" :style="{width: '100%'}" value=""
                                            clearable>
                                            <el-option v-for="item in details_warehouseLabelList" :key="item.value"
                                                :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column label="产品编号" prop="XSTDMX_WLBH" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="产品名称" align="center" width="200" sortable>
                                    <template slot-scope="scope">
                                        <el-select size="mini" v-model="scope.row.XSTDMX_WLBH" filterable remote
                                            default-first-option placeholder="请选择产品名称" :style="{width: '100%'}" value=""
                                            :remote-method="details_getProductTypeLabelList"
                                            @change="details_updateDetailsFields('XSTDMX_WLBH', scope.row, scope.$index)"
                                            clearable>
                                            <el-option v-for="item in details_productTypeLabelList" :key="item.value"
                                                :label="item.label" :value="item.value">
                                                <span style="float: left">{{ item.label }}</span>
                                                <span style="float: right; color: #8492a6; font-size: 10px">{{
                                                    item.description
                                                    }}</span>
                                            </el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                                <el-table-column label="规格型号" prop="LSWLZD_GGXH" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="分录类型" prop="XSTDMX_FLLX_MC" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="库存方式" prop="xstdmx_kcfs_mc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="库存预留" prop="XSTDMX_KCYL_mc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="交货日期" align="center" width="175" sortable>
                                    <template slot-scope="scope">
                                        <el-date-picker size="mini" type="date" v-model="scope.row.XSTDMX_JHRQ"
                                            :style="{width: '100%'}" value-format="yyyyMMdd" placeholder="请选择日期">
                                        </el-date-picker>
                                    </template>
                                </el-table-column>
                                <el-table-column label="数量" align="center" width="120" sortable>
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.XSTDMX_ZSL"
                                            @change="details_updateDetailsFields('XSTDMX_ZSL', scope.row, scope.$index)"
                                            size="mini" :style="{width: '100%'}" :controls="false"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="计量单位" prop="zmc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="辅助数量" prop="XSTDMX_FSL2" align="center" width="100" sortable>
                                </el-table-column>
                                <el-table-column label="辅助计量" prop="fmc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="含税单价" align="center" width="120" sortable>
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.XSTDMX_YZHSJ"
                                            @change="details_updateDetailsFields('XSTDMX_YZHSJ', scope.row, scope.$index)"
                                            size="mini" :style="{width: '100%'}" :controls="false"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="销售单价" align="center" width="120" sortable>
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.XSTDMX_YZXSJ"
                                            @change="details_updateDetailsFields('XSTDMX_YZXSJ', scope.row, scope.$index)"
                                            size="mini" :style="{width: '100%'}" :controls="false"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="销售金额" prop="XSTDMX_YXSE" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="税率(%)" prop="XSTDMX_SL" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="税额" prop="XSTDMX_YSE" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="订单编号" prop="XSTDMX_DDBH" align="center" width="150" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="备注" prop="remark" align="center" width="250" sortable>
                                    <template slot-scope="scope">
                                        <el-input size="mini" placeholder="请输入备注" clearable :style="{width: '100%'}"
                                            v-model.trim="scope.row.remark">
                                        </el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="分录来源" prop="xstdmx_flly_mc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="是否出库" prop="xstdmx_sfck_mc" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column label="立账日期" prop="XSTDMX_LZRQ" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.XSTDMX_LZRQ) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="预回款期" prop="XSTDMX_YHKRQ" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.XSTDMX_YHKRQ) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" fixed="right" align="center" width="100">
                                    <template slot-scope="scope">
                                        <el-tooltip content="删除分录" placement="top">
                                            <el-button type="danger" size="mini"
                                                @click="details_deleteLadingEntry(scope.row)"
                                                icon="el-icon-delete-solid" circle></el-button>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <br>
                        <!--提单信息-->
                        <div>
                            <el-form label-width="90px" size="mini">
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="审核标志">
                                            <el-input size="mini" placeholder="暂无审核标志" disabled :style="{width: '100%'}"
                                                v-model.trim="deliveryNoteDetails.XSTD_SHBZ">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="审核人">
                                            <el-input size="mini" placeholder="暂无审核人" disabled :style="{width: '100%'}"
                                                v-model.trim="deliveryNoteDetails.XSTD_SHXM">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="修改时间">
                                            <el-date-picker size="mini" :style="{width: '100%'}" disabled
                                                v-model="deliveryNoteDetails.XSTD_XGSJ" type="date"
                                                value-format="yyyyMMdd" placeholder="暂无修改时间">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="制单人">
                                            <el-input size="mini" placeholder="暂无制单人" disabled :style="{width: '100%'}"
                                                v-model.trim="deliveryNoteDetails.XSTD_ZDXM">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item label="业务标识">
                                            <el-input size="mini" placeholder="暂无业务标识" disabled :style="{width: '100%'}"
                                                v-model.trim="deliveryNoteDetails.XSTD_YWBS">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="记账人">
                                            <el-input size="mini" placeholder="暂无记账人" disabled :style="{width: '100%'}"
                                                v-model.trim="deliveryNoteDetails.XSTD_JZXM">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="记账时间">
                                            <el-date-picker size="mini" :style="{width: '100%'}" disabled
                                                v-model="deliveryNoteDetails.XSTD_JZSJ" type="date"
                                                value-format="yyyyMMdd" placeholder="暂无记账时间">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="草稿">
                                            <el-switch v-model="reversedSwitchValue" active-color="#13ce66"
                                                inactive-color="#ff4949">
                                            </el-switch>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                            <el-row type="flex" justify="center">
                                <el-tooltip
                                    v-if="!deliveryNoteDetails.XSTD_SHBZ || deliveryNoteDetails.XSTD_SHBZ === '未提交'"
                                    content="新增提单分录" placement="top">
                                    <el-button size="mini" @click="details_addLadingEntry()" type="primary"
                                        icon="el-icon-plus" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip
                                    v-if="!deliveryNoteDetails.XSTD_SHBZ || deliveryNoteDetails.XSTD_SHBZ === '未提交'"
                                    content="参照订单" placement="top">
                                    <el-button size="mini" @click="order_openOrderListDialog(false)" type="warning"
                                        icon="el-icon-s-order" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip
                                    v-if="!deliveryNoteDetails.XSTD_SHBZ || deliveryNoteDetails.XSTD_SHBZ === '未提交'"
                                    content="保存提单" placement="top">
                                    <el-button size="mini" @click="details_saveDeliveryNote()" type="success"
                                        icon="el-icon-check" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip
                                    v-if="deliveryNoteDetails.XSTD_SHBZ !== '未提交' && deliveryNoteDetails.XSTD_JZBZ === '0'"
                                    content="撤回提单" placement="top">
                                    <el-button type="warning" size="mini" @click="details_undoSubmitDeliveryNote()"
                                        icon="el-icon-refresh-left" circle></el-button>
                                </el-tooltip>
                                <el-tooltip
                                    v-if="(!deliveryNoteDetails.XSTD_SHBZ || deliveryNoteDetails.XSTD_JZBZ === '0') && deliveryNoteDetails.XSTD_TDBH"
                                    content="删除提单" placement="top">
                                    <el-button size="mini" @click="details_deleteDeliveryNote()" type="danger"
                                        icon="el-icon-delete-solid" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip content="取消" placement="top">
                                    <el-button size="mini" @click="details_closeDeliveryNote()" type="danger"
                                        icon="el-icon-close" circle>
                                    </el-button>
                                </el-tooltip>
                                <el-tooltip v-if="deliveryNoteDetails.XSTD_TDBH" content="打印提单" placement="top">
                                    <el-button type="success" size="mini" @click="details_printDeliveryNote()"
                                        icon="el-icon-printer" circle style="margin-right: 10px;"></el-button>
                                </el-tooltip>
                                <el-dropdown trigger="click" @command="details_downloadDeliveryNote">
                                    <el-tooltip v-if="deliveryNoteDetails.XSTD_TDBH" content="下载提单" placement="top">
                                        <el-button type="success" size="mini" icon="el-icon-download" circle></el-button>
                                    </el-tooltip>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item command="excel">Excel格式</el-dropdown-item>
                                        <el-dropdown-item command="pdf">PDF格式</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </el-row>
                        </div>
                    </el-dialog>
                </div>
                <!--订单列表-->
                <div>
                    <el-dialog :visible.sync="order_orderListVisible" title="订单列表" width="85%" top="5vh" center>
                        <!--订单查询-->
                        <div>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-input size="mini" placeholder="请输入我方合同号" clearable :style="{width: '100%'}"
                                        v-model.trim="orderQuery.orderNumber" @change="order_getOrderList()"
                                        prefix-icon="el-icon-search">
                                    </el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-input size="mini" placeholder="请输入对方合同号" clearable :style="{width: '100%'}"
                                        v-model.trim="orderQuery.customerOrderNumber" @change="order_getOrderList()"
                                        prefix-icon="el-icon-search">
                                    </el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-input size="mini" placeholder="请输入对方合同明细" clearable :style="{width: '100%'}"
                                        v-model.trim="orderQuery.customerOrderDetail" @change="order_getOrderList()"
                                        prefix-icon="el-icon-search">
                                    </el-input>
                                </el-col>
                                <el-col :span="6">
                                    <el-date-picker size="mini" placement="bottom-start" unlink-panels
                                        v-model="orderQuery.queryDate" type="daterange" start-placeholder="起始单据日期"
                                        end-placeholder="终止单据日期" value-format="yyyy-MM-dd" :style="{width: '100%'}"
                                        @change="order_getOrderList()" clearable>
                                    </el-date-picker>
                                </el-col>
                                <el-col :span="6">
                                    <el-tooltip content="查询销售订单" placement="top">
                                        <el-button size="mini" @click="order_getOrderList()" type="primary"
                                            icon="el-icon-search" circle>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="清空查询条件" placement="top">
                                        <el-button size="mini" @click="order_refreshOrderSearch()" type="primary"
                                            icon="el-icon-refresh" circle>
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="批量参照订单" placement="top">
                                        <el-button type="warning" @click="order_batchReferOrder()" size="mini"
                                            icon="el-icon-s-claim" circle></el-button>
                                    </el-tooltip>
                                </el-col>
                            </el-row>
                            <br>
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-select size="mini" v-model="orderQuery.customerNumberList" filterable remote
                                        multiple collapse-tags default-first-option :reserve-keyword="true"
                                        placeholder="请选择客户名称" :style="{width: '100%'}" value=""
                                        :remote-method="order_getCustomerLabelList" @change="order_getOrderList()"
                                        clearable>
                                        <el-option v-for="item in order_customerLabelList" :key="item.value"
                                            :label="item.label" :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="4">
                                    <el-input size="mini" placeholder="请输入产品类型" clearable :style="{width: '100%'}"
                                        v-model.trim="orderQuery.productType" @change="order_getOrderList()"
                                        prefix-icon="el-icon-search">
                                    </el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-select size="mini" v-model="orderQuery.departmentNumberList" filterable multiple
                                        collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择业务部门"
                                        :style="{width: '100%'}" value="" @visible-change="order_getDepartmentLabelList"
                                        @change="order_getOrderList()" clearable>
                                        <el-option v-for="item in order_departmentLabelList" :key="item.value"
                                            :label="item.label" :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="4">
                                    <el-select size="mini" v-model="orderQuery.employeeNumberList" filterable multiple
                                        collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择业务员"
                                        :style="{width: '100%'}" value="" @visible-change="order_getEmployeeLabelList"
                                        @change="order_getOrderList()" clearable>
                                        <el-option v-for="item in order_employeeLabelList" :key="item.value"
                                            :label="item.label" :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-col>
                            </el-row>
                        </div>
                        <br>
                        <!--订单列表-->
                        <div>
                            <el-table v-loading="order_tableLoading" size="mini" :data="order_orderList"
                                max-height="500" @selection-change="order_handleSelectionChange" highlight-current-row
                                :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                <el-table-column fixed type="selection" width="40">
                                </el-table-column>
                                <el-table-column fixed type="index" align="center" width="40">
                                </el-table-column>
                                <el-table-column prop="XSDDMX_DJRQ" label="单据日期" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.XSDD_DJRQ) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="XSDDMX_JHRQ" label="交货日期" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.XSDDMX_JHRQ) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="XSDD_DDBH" label="订单编号" align="center" width="150" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="XSDD_C2" label="对方合同号" align="center" width="150" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="DF_HT_MX" label="对方合同明细" align="center" width="150" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="XSDD_SHDKHMC" label="客户名称" align="center" width="200" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="LSWLZD_WLMC" label="产品名称" align="center" width="150" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="LSWLZD_GGXH" label="规格型号" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="wf_sl" label="未发数量" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="LSWBZD_BM" label="币种" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="XSDDMX_YZHSJ" label="含税单价" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="XSDDMX_YXSE" label="销售金额" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="XSDD_BZ" label="备注" align="center" width="200" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="KCBMZD_BMMC" label="业务部门" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="ZWZGZD_ZGXM" label="业务员" align="center" width="100" sortable
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" align="center" width="100"
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <el-tooltip content="参照此订单" placement="top">
                                            <el-button type="warning" size="mini" @click="order_referOrder(scope.row)"
                                                icon="el-icon-s-claim" circle></el-button>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-pagination small style="text-align: right;" layout="prev, pager, next"
                                @current-change="order_handleCurrentChange" :current-page="orderQuery.currentPage"
                                :page-size="orderQuery.pageSize" :total="orderQuery.total">
                            </el-pagination>
                        </div>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</body>
<!-- 引入组件库 -->
<!--<script src="../js/vue.js"></script>
<script src="../js/index.js"></script>
<script src="../js/axios.min.js"></script>
<script type="text/javascript" src="../js/jquery.min.js"></script>
<link rel="stylesheet" href="../element-ui/lib/theme-chalk/index.css">-->
<script src="__STATIC__/zad_js/js/vue.js"></script>
<script src="__STATIC__/zad_js/js/index.js"></script>
<script src="__STATIC__/zad_js/js/axios.min.js"></script>
<script type="text/javascript" src="__STATIC__/zad_js/js/jquery.min.js"></script>
<link rel="stylesheet" href="__STATIC__/zad_js/element-ui/lib/theme-chalk/index.css">
<script>
    let vue = new Vue({
        el: '#app',
        data: {
            /*通用*/
            fullscreenLoading: false,// 全屏加载
            user: "",// 当前用户
            /*提单列表*/
            deliveryNoteQuery: {
                currentPage: 1,//当前页码
                pageSize: 15,//每页显示的记录数
                total: 0,//总记录数

                deliveryNoteFlagList: [],// 提单标志列表
                auditFlagList: [],// 审核标志列表
                deliveryNoteNumber: "",// 提单编号
                customerNumberList: [],// 客户编号集合
                departmentNumberList: [],// 部门编号集合
                employeeNumberList: [],// 员工编号集合

                queryDate: ["", ""],// 查询日期（无需接收）
                startDate: "",// 起始日期
                endDate: "",// 截止日期
            },// 提单查询
            deliveryNoteList: [],// 提单列表
            list_deliveryNoteDetailsTableVisible: false,// 提单详情列表可见性
            list_ladingEntryList: [],// 提单分录
            list_selectedDeliveryNoteList: [],// 提单列表被选中的提单集合
            list_deliveryNoteFlagLabelList: [],// 提单标志选项集合
            list_auditFlagLabelList: [],// 审核标志选项集合
            list_customerLabelList: [],// 提单客户选项集合
            list_departmentLabelList: [],// 提单部门选项集合
            list_employeeLabelList: [],// 提单员工选项集合
            list_tableLoading: false,
            list_ladingEntryLoading: false,

            /*提单详情*/
            deliveryNoteDetails: {},// 提单详情
            details_deliveryNoteDetailsVisible: false,// 提单详情弹窗可见性
            details_deliveryNoteTypeLabelList: [],// 提单种类选项集合
            details_soldToCustomerLabelList: [],// 售达客户选项集合
            details_shippedToCustomerLabelList: [],// 送达客户选项集合
            details_invoiceToCustomerLabelList: [],// 收票客户选项集合
            details_paymentCustomerLabelList: [],// 付款客户选项集合
            details_auditFlagLabelList: [],// 审核标志选项集合
            details_currencyTypeLabelList: [],// 币种选项集合
            details_exchangeRateLabelList: [],// 汇率选项集合
            details_departmentLabelList: [],// 提单详情部门选项集合
            details_employeeLabelList: [],// 提单详情员工选项集合
            details_businessTypeLabelList: [],// 业务类型选项集合
            details_discountPolicyLabelList: [],// 折扣政策选项集合
            details_customerPhoneLabelList: [],// 客户电话选项集合
            details_customerAddressLabelList: [],// 客户地址选项集合
            details_destinationLabelList: [],// 到货地点选项集合
            details_contactLabelList: [],// 联系人选项集合
            details_transportationLabelList: [],// 运输方式选项集合
            details_remarksLabelList: [],// 备注选项集合
            details_carrierLabelList: [],// 承运人选项集合
            details_customerContractNumberLabelList: [],// 客户合同号选项集合
            details_waybillNumberLabelList: [],// 提运单号选项集合
            /*详情分录*/
            details_productTypeLabelList: [],// 产品类型选项集合
            details_warehouseLabelList: [],// 仓库名称选项集合
            /*订单列表*/
            orderQuery: {
                currentPage: 1,//当前页码
                pageSize: 50,//每页显示的记录数
                total: 0,//总记录数

                orderInformationList: [],
                orderNumber: "",// 订单编号
                customerOrderNumber: "",// 对方合同号
                customerOrderDetail: "",// 对方合同明细
                productType: "",// 产品类型
                customerNumberList: [],// 客户编号集合
                departmentNumberList: [],// 部门编号集合
                employeeNumberList: [],// 员工编号集合

                queryDate: ["", ""],// 查询日期（无需接收）
                startDate: "",// 起始日期
                endDate: "",// 截止日期
            },// 订单查询
            order_orderList: [], // 订单列表
            order_selectedOrderList: [],// 订单列表选中项
            order_orderListVisible: false,// 订单列表弹窗可见性
            order_customerLabelList: [],//
            order_departmentLabelList: [],
            order_employeeLabelList: [],
            order_tableLoading: false,

            order_currentNotification: null,
        },

        created() {
            // this.getCurrentUser();
            // this.getListDeliveryNoteFlagLabelList();
            // this.getListAuditFlagLabelList();
            // this.getListDepartmentLabelList();
            // this.getListEmployeeLabelList();
            this.getDeliveryNoteList();
        },

        computed: {
            // 反转草稿switch结果显示
            reversedSwitchValue: {
                get() {
                    return !this.deliveryNoteDetails.XSTD_SFQR_BIT;
                },
                set(newValue) {
                    if (newValue) {
                        this.deliveryNoteDetails.XSTD_SFQR = 0;
                    } else {
                        this.deliveryNoteDetails.XSTD_SFQR = 1;
                    }
                    this.deliveryNoteDetails.XSTD_SFQR_BIT = !newValue;
                },
            },
        },

        methods: {
            /*通用*/
            // 获取当前用户
            getCurrentUser() {
                return axios.post("/sale/order/getCurrentUser").then((res) => {
                    if (res.data.flag) {
                        this.user = res.data.data;
                    } else {
                        this.$message.error("用户信息获取失败");
                    }
                    return res.data.flag;
                });
            },

            /*提单列表*/
            // 获取提单列表
            getDeliveryNoteList() {
                this.list_tableLoading = true;
                if (this.deliveryNoteQuery.queryDate == null) {
                    this.deliveryNoteQuery.queryDate = ["", ""];
                }
                this.deliveryNoteQuery.startDate = this.deliveryNoteQuery.queryDate[0];
                this.deliveryNoteQuery.endDate = this.deliveryNoteQuery.queryDate[1];
                axios.post("/sale/fahuo/getDeliveryNoteList", this.deliveryNoteQuery).then((res) => {
                    if (res.data.flag) {
                        this.deliveryNoteQuery.currentPage = Number(res.data.data.currentPage);
                        this.deliveryNoteQuery.pageSize = Number(res.data.data.pageSize);
                        this.deliveryNoteQuery.total = Number(res.data.data.total);
                        this.deliveryNoteList = res.data.data.records || [];
                    } else {
                        this.deliveryNoteList = [];
                        this.$message.error(res.data.msg);
                    }
                }).finally(() => {
                    this.list_tableLoading = false;
                });
            },

            // 清空提单列表查询条件
            refreshListSearch() {
                this.deliveryNoteQuery = {
                    currentPage: 1,//当前页码
                    pageSize: 15,//每页显示的记录数
                    total: 0,//总记录数

                    deliveryNoteFlagList: [],// 提单标志列表
                    auditFlagList: [],// 审核标志列表
                    deliveryNoteNumber: "",// 提单编号
                    customerNumberList: [],// 客户编号集合
                    departmentNumberList: [],// 部门编号集合
                    employeeNumberList: [],// 员工编号集合

                    queryDate: ["", ""],// 查询日期（无需接收）
                    startDate: "",// 起始日期
                    endDate: "",// 截止日期
                }
                if (this.list_deliveryNoteDetailsTableVisible) {
                    this.deliveryNoteQuery.pageSize = 8;
                } else {
                    this.deliveryNoteQuery.pageSize = 15;
                }
                this.getDeliveryNoteList();
            },

            // 显示提单详情
            switchListDeliveryNoteDetails() {
                if (this.list_deliveryNoteDetailsTableVisible) {
                    this.list_deliveryNoteDetailsTableVisible = false;
                    this.deliveryNoteQuery.pageSize = 15;
                    this.getDeliveryNoteList();
                } else {
                    this.list_deliveryNoteDetailsTableVisible = true;
                    this.deliveryNoteQuery.pageSize = 8;
                    this.getDeliveryNoteList();
                }
            },

            // 列表_提交提单
            list_submitDeliveryNote(row) {
                if (!row) {
                    if (this.list_selectedDeliveryNoteList.length === 0) {
                        this.$message({
                            type: 'warning',
                            message: '请选择需要提交的提单'
                        });
                        return;
                    }
                }
                let hasInvalidItem = false;
                this.list_selectedDeliveryNoteList.forEach(item => {
                    if (item.XSTD_SHBZ !== "未提交") {
                        hasInvalidItem = true;
                    }
                });
                if (hasInvalidItem) {
                    this.$message({
                        type: 'warning',
                        message: '请选择未提交提单'
                    });
                    return;
                }
                this.$confirm('是否提交选中提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let deliveryNoteList = [];
                    if (row) {
                        deliveryNoteList.push(row);
                    } else {
                        deliveryNoteList = this.list_selectedDeliveryNoteList;
                    }
                    deliveryNoteList.forEach(item => {
                        item.XSTD_SFQR = "1";
                    });
                    axios.post("/sale/fahuo/saveDeliveryNote", deliveryNoteList).then((res) => {
                        if (res.data.flag) {
                            this.$message.success("提单提交成功");
                        } else {
                            this.$message.error("提单提交失败");
                        }
                    }).finally(() => {
                        // 重置提单详情
                        this.deliveryNoteDetails = {};
                        // 关闭提单窗口
                        this.details_deliveryNoteDetailsVisible = false;
                        // 刷新列表
                        this.getDeliveryNoteList();
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 列表_取消提交提单
            list_undoSubmitDeliveryNote(row) {
                if (!row) {
                    if (this.list_selectedDeliveryNoteList.length === 0) {
                        this.$message({
                            type: 'warning',
                            message: '请选择需要撤回的提单'
                        });
                        return;
                    }
                }
                let hasInvalidItem = false;
                this.list_selectedDeliveryNoteList.forEach(item => {
                    if (item.XSTD_SHBZ === "未提交") {
                        hasInvalidItem = true;
                    }
                });
                if (hasInvalidItem) {
                    this.$message({
                        type: 'warning',
                        message: '请选择已提交提单'
                    });
                    return;
                }
                this.$confirm('是否撤回选中提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let deliveryNoteList = [];
                    if (row) {
                        deliveryNoteList.push(row);
                    } else {
                        deliveryNoteList = this.list_selectedDeliveryNoteList;
                    }
                    axios.post("/sale/fahuo/undoSubmitDeliveryNote", deliveryNoteList).then((res) => {
                        if (res.data.flag) {
                            this.$message.success("提单撤回成功");
                        } else {
                            this.$message.error("提单撤回失败");
                        }
                    }).finally(() => {
                        // 重置提单详情
                        this.deliveryNoteDetails = {};
                        // 关闭提单窗口
                        this.details_deliveryNoteDetailsVisible = false;
                        // 刷新提单列表
                        this.getDeliveryNoteList();
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 列表_删除提单
            list_deleteDeliveryNote(row) {
                // 判断是否为批量删除
                if (!row) {
                    // 校验是否勾选提单
                    if (this.list_selectedDeliveryNoteList.length === 0) {
                        this.$message({
                            type: 'warning',
                            message: '请选择需要提交的提单'
                        });
                        return;
                    }
                }
                this.$confirm('是否删除选中提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 构建待删除提单集合
                    let deliveryNoteList = [];
                    if (row) {
                        // 非批量删除，添加行至集合
                        deliveryNoteList.push(row);
                    } else {
                        deliveryNoteList = this.list_selectedDeliveryNoteList;
                    }
                    axios.post("/sale/fahuo/deleteDeliveryNote", deliveryNoteList).then((res) => {
                        if (res.data.flag) {
                            this.$message.success("提单删除成功");
                        } else {
                            this.$message.error("提单删除失败");
                        }
                    }).finally(() => {
                        // 重置提单详情
                        this.deliveryNoteDetails = {};
                        // 关闭提单窗口
                        this.details_deliveryNoteDetailsVisible = false;
                        // 刷新提单列表
                        this.getDeliveryNoteList();
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 行点击事件-显示提单详情
            showListDeliveryNoteDetails(row, column, event) {
                if (this.list_deliveryNoteDetailsTableVisible && column.label !== "操作") {
                    this.list_ladingEntryLoading = true;
                    axios.post("/sale/fahuo/getDeliveryNoteDetails", row).then((res) => {
                        if (res.data.flag) {
                            this.list_ladingEntryList = res.data.data.ladingEntryList;
                            this.list_ladingEntryList = res.data.data.ladingEntryList;
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    }).finally(() => {
                        this.list_ladingEntryLoading = false;
                    });
                }
            },

            // 提单列表切换页码
            handleListCurrentChange(currentPage) {
                this.deliveryNoteQuery.currentPage = currentPage;
                this.getDeliveryNoteList()
            },

            // 列表_选中项变化
            list_handleSelectionChange(val) {
                this.list_selectedDeliveryNoteList = val;
            },

            // 列表_选择项是否可选
            list_selectable(row, index) {
                if (row.XSTD_JZBZ !== '未记账') {
                    return false;
                }
                return true;
            },

            // 打印提单方法
            list_printDeliveryNote(row) {
                // 显示加载状态
                this.fullscreenLoading = true;

                axios({
                    method: 'post',
                    url: '/sale/fahuo/getDeliveryNoteDetails',  // 后端接口地址
                    data: row
                })
                    .then(response => {
                        if (response.data.flag) {
                            // 发送请求到后端
                            axios({
                                method: 'post',
                                url: 'http://172.16.99.22/sales/deliveryNote/getDeliveryNotePDF/1',  // 后端接口地址
                                // url: 'http://192.168.3.106/sales/deliveryNote/getDeliveryNotePDF/1',  // 后端接口地址
                                data: response.data.data,
                                responseType: 'blob'  // 指定返回类型为二进制数据
                            })
                                .then(response => {
                                    // 创建Blob对象
                                    const blob = new Blob([response.data], { type: 'application/pdf' });
                                    // 创建URL
                                    const url = URL.createObjectURL(blob);

                                    // 创建iframe
                                    const iframe = document.createElement('iframe');
                                    iframe.style.display = 'none';
                                    iframe.src = url;

                                    // 添加事件监听器
                                    iframe.onload = function () {
                                        console.log('PDF加载完成，准备打印...');
                                        try {
                                            // 确保iframe内容已加载
                                            if (iframe.contentWindow && iframe.contentWindow.document.readyState === 'complete') {
                                                console.log('调用打印功能...');
                                                iframe.contentWindow.focus(); // 确保iframe获得焦点
                                                iframe.contentWindow.print();

                                                // 监听打印完成事件
                                                iframe.contentWindow.onafterprint = function () {
                                                    console.log('打印完成，清理资源...');
                                                    // 延迟清理资源
                                                    setTimeout(() => {
                                                        document.body.removeChild(iframe);
                                                        URL.revokeObjectURL(url);
                                                    }, 1000);
                                                };
                                            } else {
                                                console.error('iframe内容未完全加载');
                                            }
                                        } catch (error) {
                                            console.error('打印出错:', error);
                                            this.$message.error('打印失败：' + error.message);
                                        }
                                    };

                                    // 添加iframe到文档
                                    document.body.appendChild(iframe);
                                    console.log('iframe已添加到文档');
                                })
                                .catch(error => {
                                    console.error('打印失败:', error);
                                    this.$message.error('打印失败，请稍后重试');
                                })
                                .finally(() => {
                                    // 关闭加载状态
                                    this.fullscreenLoading = false;
                                });
                        } else {
                            this.$message.error(response.data.msg);
                        }
                    })
            },

            // 下载提单方法
            list_downloadDeliveryNote(row, type) {
                // 显示加载状态
                this.fullscreenLoading = true;

                axios({
                    method: 'post',
                    url: '/sale/fahuo/getDeliveryNoteDetails',  // 获取提单详情
                    data: row
                })
                    .then(response => {
                        if (response.data.flag) {
                            // 发送请求获取文件
                            axios({
                                method: 'post',
                                url: `http://172.16.99.22/sales/deliveryNote/getDeliveryNotePDF/${type === 'excel' ? '2' : '1'}`,
                                data: response.data.data,
                                responseType: 'blob'  // 指定返回类型为二进制数据
                            })
                                .then(response => {
                                    // 创建Blob对象
                                    const blob = new Blob([response.data], {
                                        type: type === 'excel' ?
                                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' :
                                            'application/pdf'
                                    });

                                    // 创建下载链接
                                    const link = document.createElement('a');
                                    link.href = URL.createObjectURL(blob);

                                    // 设置下载文件名
                                    const fileName = `销售提单_${row.XSTD_TDBH || 'document'}.${type === 'excel' ? 'xlsx' : 'pdf'}`;
                                    link.download = fileName;

                                    // 触发下载
                                    link.click();

                                    // 清理资源
                                    URL.revokeObjectURL(link.href);
                                })
                                .catch(error => {
                                    console.error('下载失败:', error);
                                    this.$message.error('下载失败，请稍后重试');
                                })
                                .finally(() => {
                                    // 关闭加载状态
                                    this.fullscreenLoading = false;
                                });
                        } else {
                            this.$message.error(response.data.msg);
                        }
                    })
                    .catch(error => {
                        console.error('获取详情失败:', error);
                        this.$message.error('获取提单详情失败');
                        this.fullscreenLoading = false;
                    });
            },

            // 打印提单方法
            details_printDeliveryNote() {
                // 显示加载状态
                this.fullscreenLoading = true;

                // 发送请求到后端
                axios({
                    method: 'post',
                    url: 'http://172.16.99.22/sales/deliveryNote/getDeliveryNotePDF/1',  // 后端接口地址
                    // url: 'http://192.168.3.106/sales/deliveryNote/getDeliveryNotePDF/1',  // 后端接口地址
                    data: this.deliveryNoteDetails,
                    responseType: 'blob'  // 指定返回类型为二进制数据
                })
                    .then(response => {
                        // 创建Blob对象
                        const blob = new Blob([response.data], { type: 'application/pdf' });
                        // 创建URL
                        const url = URL.createObjectURL(blob);

                        // 创建iframe
                        const iframe = document.createElement('iframe');
                        iframe.style.display = 'none';
                        iframe.src = url;

                        // 添加事件监听器
                        iframe.onload = function () {
                            console.log('PDF加载完成，准备打印...');
                            try {
                                // 确保iframe内容已加载
                                if (iframe.contentWindow && iframe.contentWindow.document.readyState === 'complete') {
                                    console.log('调用打印功能...');
                                    iframe.contentWindow.focus(); // 确保iframe获得焦点
                                    iframe.contentWindow.print();

                                    // 监听打印完成事件
                                    iframe.contentWindow.onafterprint = function () {
                                        console.log('打印完成，清理资源...');
                                        // 延迟清理资源
                                        setTimeout(() => {
                                            document.body.removeChild(iframe);
                                            URL.revokeObjectURL(url);
                                        }, 1000);
                                    };
                                } else {
                                    console.error('iframe内容未完全加载');
                                }
                            } catch (error) {
                                console.error('打印出错:', error);
                                this.$message.error('打印失败：' + error.message);
                            }
                        };

                        // 添加iframe到文档
                        document.body.appendChild(iframe);
                        console.log('iframe已添加到文档');
                    })
                    .catch(error => {
                        console.error('打印失败:', error);
                        this.$message.error('打印失败，请稍后重试');
                    })
                    .finally(() => {
                        // 关闭加载状态
                        this.fullscreenLoading = false;
                    });
            },

            // 下载提单方法
            details_downloadDeliveryNote(type) {
                // 显示加载状态
                this.fullscreenLoading = true;

                // 发送请求获取文件
                axios({
                    method: 'post',
                    url: `http://172.16.99.22/sales/deliveryNote/getDeliveryNotePDF/${type === 'excel' ? '2' : '1'}`,
                    data: this.deliveryNoteDetails,
                    responseType: 'blob'  // 指定返回类型为二进制数据
                })
                    .then(response => {
                        // 创建Blob对象
                        const blob = new Blob([response.data], {
                            type: type === 'excel' ?
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' :
                                'application/pdf'
                        });

                        // 创建下载链接
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);

                        // 设置下载文件名
                        const fileName = `销售提单_${this.deliveryNoteDetails.XSTD_TDBH || 'document'}.${type === 'excel' ? 'xlsx' : 'pdf'}`;
                        link.download = fileName;

                        // 触发下载
                        link.click();

                        // 清理资源
                        URL.revokeObjectURL(link.href);
                    })
                    .catch(error => {
                        console.error('下载失败:', error);
                        this.$message.error('下载失败，请稍后重试');
                    })
                    .finally(() => {
                        // 关闭加载状态
                        this.fullscreenLoading = false;
                    });
            },

            // 获取列表提单标志选项集合
            getListDeliveryNoteFlagLabelList(visible) {
                console.log(visible)
                if (visible) {
                    axios.post("/sale/fahuo/getDeliveryNoteFlagLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.list_deliveryNoteFlagLabelList = res.data.data || [];
                        } else {
                            this.list_deliveryNoteFlagLabelList = [];
                            this.$message.error("提单标志选项获取失败");
                        }
                    }).catch(() => {
                        this.list_deliveryNoteFlagLabelList = [];
                        this.$message.error("提单标志选项获取失败");
                    });
                }
            },

            // 获取列表审核标志选项集合
            getListAuditFlagLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getAuditFlagLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.list_auditFlagLabelList = res.data.data || [];
                        } else {
                            this.list_auditFlagLabelList = [];
                            this.$message.error("审核标志选项获取失败");
                        }
                    }).catch(() => {
                        this.list_auditFlagLabelList = [];
                        this.$message.error("审核标志选项获取失败");
                    });
                }
            },

            // 获取列表客户选项集合
            getListCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.list_customerLabelList = res.data.data || [];
                        } else {
                            this.list_customerLabelList = [];
                            this.$message.error("客户获取失败");
                        }
                    }).catch(() => {
                        this.list_customerLabelList = [];
                        this.$message.error("客户获取失败");
                    });
                }
            },

            // 获取列表部门选项集合
            getListDepartmentLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDepartmentLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.list_departmentLabelList = (res.data.data || []).filter(item => {
                                return item && item.label && (item.label.includes('销售') || item.label.includes('贸易'));
                            });
                        } else {
                            this.list_departmentLabelList = [];
                            this.$message.error(res.data.msg || "部门选项获取失败");
                        }
                    }).catch(() => {
                        this.list_departmentLabelList = [];
                        this.$message.error("部门选项获取失败");
                    });
                }
            },

            // 获取列表业务员选项集合
            getListEmployeeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getEmployeeLabelList", this.deliveryNoteQuery.departmentNumberList).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.list_employeeLabelList = res.data.data || [];
                        } else {
                            this.list_employeeLabelList = [];
                            this.$message.error(res.data.msg || "业务员选项获取失败");
                        }
                    }).catch(() => {
                        this.list_employeeLabelList = [];
                        this.$message.error("业务员选项获取失败");
                    });
                }
            },

            /*提单详情*/
            // 提单_打开提单详情弹窗
            details_openDeliveryNoteDetailsDialog(creat, row) {
                if (creat) {
                    this.deliveryNoteDetails = {};
                    this.$set(this.deliveryNoteDetails, 'XSTD_ZLBH', "01");
                    // 设定默认日期
                    this.$set(this.deliveryNoteDetails, 'XSTD_DJRQ', this.util_formatDetailsDate(new Date()));
                    this.$set(this.deliveryNoteDetails, 'XSTD_YWRQ', this.util_formatDetailsDate(new Date()));
                    // 设定草稿状态（默认非草稿）
                    this.$set(this.deliveryNoteDetails, 'XSTD_SFQR', 1);
                    this.$set(this.deliveryNoteDetails, 'XSTD_SFQR_BIT', true);
                    // 提单详情选项请求
                    this.details_getDeliveryNoteTypeLabelList(true);// 提单类型选项
                    this.details_getCurrencyTypeLabelList(true);// 币种选项
                    this.details_getDepartmentLabelList(true);// 部门选项
                    this.details_getEmployeeLabelList(true);// 业务员选项
                    this.details_getBusinessTypeLabelList(true);// 业务类型选项
                    this.details_getDiscountPolicyLabelList(true);// 折扣政策选项
                    this.details_getTransportationLabelList(true);// 运输方式选项
                    // 提单分录选项请求
                    this.details_getWarehouseLabelList(true);
                    this.details_deliveryNoteDetailsVisible = true;
                } else {
                    axios.post("/sale/fahuo/getDeliveryNoteDetails", row).then((res) => {
                        if (res.data.flag) {
                            this.deliveryNoteDetails = res.data.data;
                            this.deliveryNoteDetails.ladingEntryList = res.data.data.ladingEntryList;
                            // 构建客户选项列表（添加空值检查）
                            this.details_soldToCustomerLabelList = this.deliveryNoteDetails.XSTD_SHDKH ? [{
                                label: this.deliveryNoteDetails.XSTD_SHDKHMC || '',
                                value: this.deliveryNoteDetails.XSTD_SHDKH
                            }] : [];
                            this.details_shippedToCustomerLabelList = this.deliveryNoteDetails.XSTD_SODKH ? [{
                                label: this.deliveryNoteDetails.XSTD_SODKHMC || '',
                                value: this.deliveryNoteDetails.XSTD_SODKH
                            }] : [];
                            this.details_invoiceToCustomerLabelList = this.deliveryNoteDetails.XSTD_SPKH ? [{
                                label: this.deliveryNoteDetails.XSTD_SPKHMC || '',
                                value: this.deliveryNoteDetails.XSTD_SPKH
                            }] : [];
                            this.details_paymentCustomerLabelList = this.deliveryNoteDetails.XSTD_FKKH ? [{
                                label: this.deliveryNoteDetails.XSTD_FKKHMC || '',
                                value: this.deliveryNoteDetails.XSTD_FKKH
                            }] : [];
                            // 构建产品类型选项集合（添加空值检查）
                            this.details_productTypeLabelList = [];
                            const uniqueProductNumber = new Set();
                            // 遍历 ladingEntryList 数组
                            if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                                this.deliveryNoteDetails.ladingEntryList.forEach(entry => {
                                    const productNumber = entry.XSTDMX_WLBH;
                                    // 如果产品编号不在 Set 中，则添加到 details_productTypeLabelList 并更新 Set
                                    if (productNumber && !uniqueProductNumber.has(productNumber)) {
                                        uniqueProductNumber.add(productNumber);
                                        this.details_productTypeLabelList.push({
                                            label: entry.lswlzd_wlmc || '',
                                            value: entry.XSTDMX_WLBH,
                                            description: entry.LSWLZD_GGXH || ''
                                        });
                                    }
                                });
                            }
                            // 提单详情选项请求
                            this.details_getDeliveryNoteTypeLabelList(true);// 提单类型选项
                            this.details_getCurrencyTypeLabelList(true);// 币种选项
                            this.details_getDepartmentLabelList(true);// 部门选项
                            this.details_getEmployeeLabelList(true);// 业务员选项
                            this.details_getBusinessTypeLabelList(true);// 业务类型选项
                            this.details_getDiscountPolicyLabelList(true);// 折扣政策选项
                            this.details_getCustomerPhoneLabelList(true);// 客户电话选项
                            this.details_getCustomerAddressLabelList(true);// 客户地址选项
                            this.details_getDestinationLabelList(true);// 到货地点选项
                            this.details_getContactLabelList(true);// 联系人选项
                            this.details_getTransportationLabelList(true);// 运输方式选项
                            // 提单分录选项请求
                            this.details_getWarehouseLabelList(true);

                            this.details_deliveryNoteDetailsVisible = true;
                        } else {
                            this.$message.error(res.data.msg);
                            this.getDeliveryNoteList();
                        }
                    });
                }
            },

            // 提单_获取提单详情提单种类选项集合
            details_getDeliveryNoteTypeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDeliveryNoteTypeLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_deliveryNoteTypeLabelList = res.data.data || [];
                        } else {
                            this.details_deliveryNoteTypeLabelList = [];
                            this.$message.error("提单种类获取失败");
                        }
                    }).catch(() => {
                        this.details_deliveryNoteTypeLabelList = [];
                        this.$message.error("提单种类获取失败");
                    });
                }
            },

            // 提单_获取提单详情售达客户选项集合
            details_getSoldToCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_soldToCustomerLabelList = res.data.data || [];
                        } else {
                            this.details_soldToCustomerLabelList = [];
                            this.$message.error("售达客户获取失败");
                        }
                    }).catch(() => {
                        this.details_soldToCustomerLabelList = [];
                        this.$message.error("售达客户获取失败");
                    });
                }
            },

            // 提单_改变售达客户
            details_changeSoldToCustomer() {
                // 改变相关选项
                this.details_shippedToCustomerLabelList = this.details_soldToCustomerLabelList;
                this.details_invoiceToCustomerLabelList = this.details_soldToCustomerLabelList;
                this.details_paymentCustomerLabelList = this.details_soldToCustomerLabelList;
                // 改变相关客户编号
                this.deliveryNoteDetails.XSTD_SODKH = this.deliveryNoteDetails.XSTD_SHDKH;
                this.deliveryNoteDetails.XSTD_SPKH = this.deliveryNoteDetails.XSTD_SHDKH;
                this.deliveryNoteDetails.XSTD_FKKH = this.deliveryNoteDetails.XSTD_SHDKH;
                // 改变相关客户名称
                const selectedCustomer = this.details_soldToCustomerLabelList.filter(item => item.value === this.deliveryNoteDetails.XSTD_SHDKH);
                if (selectedCustomer && selectedCustomer.length > 0) {
                    this.deliveryNoteDetails.XSTD_SHDKHMC = selectedCustomer[0].label;
                }
                this.deliveryNoteDetails.XSTD_SODKHMC = this.deliveryNoteDetails.XSTD_SHDKHMC;
                this.deliveryNoteDetails.XSTD_SPKHMC = this.deliveryNoteDetails.XSTD_SHDKHMC;
                this.deliveryNoteDetails.XSTD_FKKHMC = this.deliveryNoteDetails.XSTD_SHDKHMC;
                // 执行客户相关请求
                this.details_getCustomerPhoneLabelList(true);// 客户电话选项
                this.details_getCustomerAddressLabelList(true);// 客户地址选项
                this.details_getDestinationLabelList(true);// 到货地点选项
                this.details_getContactLabelList(true);// 联系人选项

            },

            // 提单_获取提单详情送达客户选项集合
            details_getShippedToCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_shippedToCustomerLabelList = res.data.data || [];
                        } else {
                            this.details_shippedToCustomerLabelList = [];
                            this.$message.error("送达客户获取失败");
                        }
                    }).catch(() => {
                        this.details_shippedToCustomerLabelList = [];
                        this.$message.error("送达客户获取失败");
                    });
                }
            },

            // 提单_获取提单详情收票客户选项集合
            details_getInvoiceToCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_invoiceToCustomerLabelList = res.data.data || [];
                        } else {
                            this.details_invoiceToCustomerLabelList = [];
                            this.$message.error("收票客户获取失败");
                        }
                    }).catch(() => {
                        this.details_invoiceToCustomerLabelList = [];
                        this.$message.error("收票客户获取失败");
                    });
                }
            },

            // 提单_获取提单详情付款客户选项集合
            details_getPaymentCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_paymentCustomerLabelList = res.data.data || [];
                        } else {
                            this.details_paymentCustomerLabelList = [];
                            this.$message.error("付款客户获取失败");
                        }
                    }).catch(() => {
                        this.details_paymentCustomerLabelList = [];
                        this.$message.error("付款客户获取失败");
                    });
                }
            },

            // 提单_获取提单详情币种选项集合
            details_getCurrencyTypeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getCurrencyTypeLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_currencyTypeLabelList = res.data.data || [];
                        } else {
                            this.details_currencyTypeLabelList = [];
                            this.$message.error("币种获取失败");
                        }
                    }).catch(() => {
                        this.details_currencyTypeLabelList = [];
                        this.$message.error("币种获取失败");
                    });
                }
            },

            // 提单_获取提单详情汇率
            details_getExchangeRate() {
                axios.post("/sale/fahuo/getExchangeRate", this.deliveryNoteDetails).then((res) => {
                    if (res.data.flag) {
                        this.deliveryNoteDetails = res.data.data;
                        this.details_updateExchangeRate();
                    } else {
                        this.$message.error("汇率获取失败");
                    }
                });
            },

            // 提单_修改汇率
            details_updateExchangeRate() {
                if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                    let index = 0;
                    this.deliveryNoteDetails.ladingEntryList.forEach(item => {
                        this.details_updateDetailsFields("XSTD_HL", item, index);
                        index++;
                    });
                }
            },

            // 提单_获取提单详情部门选项集合
            details_getDepartmentLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDepartmentLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_departmentLabelList = (res.data.data || []).filter(item => {
                                return item && item.label && (item.label.includes('销售') || item.label.includes('贸易'));
                            });
                        } else {
                            this.details_departmentLabelList = [];
                            this.$message.error("业务部门获取失败");
                        }
                    }).catch(() => {
                        this.details_departmentLabelList = [];
                        this.$message.error("业务部门获取失败");
                    });
                }
            },

            // 提单_提单详情部门选择改变
            details_changeDepartmentSelect() {
                this.$set(this.deliveryNoteDetails, 'XSTD_RYBH', '');
            },

            // 提单_获取提单详情业务员选项集合
            details_getEmployeeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getEmployeeLabelList", this.deliveryNoteDetails.XSTD_BMBH ? [this.deliveryNoteDetails.XSTD_BMBH] : []).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_employeeLabelList = res.data.data || [];
                        } else {
                            this.details_employeeLabelList = [];
                            this.$message.error(res.data.msg || "业务员选项获取失败");
                        }
                    }).catch(() => {
                        this.details_employeeLabelList = [];
                        this.$message.error("业务员选项获取失败");
                    });
                }
            },

            // 提单_获取提单详情业务类型选项集合
            details_getBusinessTypeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getBusinessTypeLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_businessTypeLabelList = res.data.data || [];
                        } else {
                            this.details_businessTypeLabelList = [];
                            this.$message.error("业务类型获取失败");
                        }
                    }).catch(() => {
                        this.details_businessTypeLabelList = [];
                        this.$message.error("业务类型获取失败");
                    });
                }
            },

            // 提单_获取提单详情折扣政策选项集合
            details_getDiscountPolicyLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDiscountPolicyLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_discountPolicyLabelList = res.data.data || [];
                        } else {
                            this.details_discountPolicyLabelList = [];
                            this.$message.error("折扣政策获取失败");
                        }
                    }).catch(() => {
                        this.details_discountPolicyLabelList = [];
                        this.$message.error("折扣政策获取失败");
                    });
                }
            },

            // 提单_获取提单详情客户电话选项集合
            details_getCustomerPhoneLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getCustomerPhoneLabelList", this.deliveryNoteDetails).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_customerPhoneLabelList = res.data.data || [];
                            if (this.details_customerPhoneLabelList && this.details_customerPhoneLabelList.length > 0) {
                                // 没有提单编号时才设置默认值
                                if (!this.deliveryNoteDetails.XSTD_TDBH) {
                                    this.$set(this.deliveryNoteDetails, 'XSTD_TELE', this.details_customerPhoneLabelList[0].value);
                                }
                            }
                        } else {
                            this.details_customerPhoneLabelList = [];
                            this.$message.error("客户电话获取失败");
                        }
                    }).catch(() => {
                        this.details_customerPhoneLabelList = [];
                        this.$message.error("客户电话获取失败");
                    });
                }
            },

            // 提单_获取提单详情客户地址选项集合
            details_getCustomerAddressLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getCustomerAddressLabelList", this.deliveryNoteDetails).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_customerAddressLabelList = res.data.data || [];
                            if (this.details_customerAddressLabelList && this.details_customerAddressLabelList.length > 0) {
                                // 没有提单编号时才设置默认值
                                if (!this.deliveryNoteDetails.XSTD_TDBH) {
                                    this.$set(this.deliveryNoteDetails, 'XSTD_ADDR', this.details_customerAddressLabelList[0].value);
                                }
                            }
                        } else {
                            this.details_customerAddressLabelList = [];
                            this.$message.error("客户地址获取失败");
                        }
                    }).catch(() => {
                        this.details_customerAddressLabelList = [];
                        this.$message.error("客户地址获取失败");
                    });
                }
            },

            // 提单_获取提单详情到货地点选项集合
            details_getDestinationLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDestinationLabelList", this.deliveryNoteDetails).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_destinationLabelList = res.data.data || [];
                            if (this.details_destinationLabelList && this.details_destinationLabelList.length > 0) {
                                // 没有提单编号时才设置默认值
                                if (!this.deliveryNoteDetails.XSTD_TDBH) {
                                    this.$set(this.deliveryNoteDetails, 'XSTD_DHDD', this.details_destinationLabelList[0].value);
                                }
                            }
                        } else {
                            this.details_destinationLabelList = [];
                            this.$message.error("到货地点获取失败");
                        }
                    }).catch(() => {
                        this.details_destinationLabelList = [];
                        this.$message.error("到货地点获取失败");
                    });
                }
            },

            // 提单_获取提单详情联系人选项集合
            details_getContactLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getContactLabelList", this.deliveryNoteDetails).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_contactLabelList = res.data.data || [];
                            if (this.details_contactLabelList && this.details_contactLabelList.length > 0) {
                                // 没有提单编号时才设置默认值
                                if (!this.deliveryNoteDetails.XSTD_TDBH) {
                                    this.$set(this.deliveryNoteDetails, 'XSTD_LXR', this.details_contactLabelList[0].value);
                                }
                            }
                        } else {
                            this.details_contactLabelList = [];
                            this.$message.error("联系人获取失败");
                        }
                    }).catch(() => {
                        this.details_contactLabelList = [];
                        this.$message.error("联系人获取失败");
                    });
                }
            },

            // 提单_获取提单详情运输方式选项集合
            details_getTransportationLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getTransportationLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_transportationLabelList = res.data.data || [];
                            // 没有提单编号时才设置默认值
                            if (!this.deliveryNoteDetails.XSTD_TDBH && this.details_transportationLabelList.length > 0) {
                                this.$set(this.deliveryNoteDetails, 'XSYSFS_FSBH', this.details_transportationLabelList[0].value);
                            }
                        } else {
                            this.details_transportationLabelList = [];
                            this.$message.error("运输方式获取失败");
                        }
                    }).catch(() => {
                        this.details_transportationLabelList = [];
                        this.$message.error("运输方式获取失败");
                    });
                }
            },

            ///提单_获取提单详情客户合同号选项集合
            details_getCustomerContractNumberLabelList(visible) {
                if (visible) {
                    // 重置选项列表
                    this.details_customerContractNumberLabelList = [];

                    const uniqueContractNumbers = new Set();

                    // 先添加当前选中的合同号（如果存在）
                    if (this.deliveryNoteDetails.XSTD_C2) {
                        uniqueContractNumbers.add(this.deliveryNoteDetails.XSTD_C2);
                        this.details_customerContractNumberLabelList = [{
                            label: this.deliveryNoteDetails.XSTD_C2,
                            value: this.deliveryNoteDetails.XSTD_C2
                        }];
                    } else {
                        this.details_customerContractNumberLabelList = [];
                    }

                    // 遍历ladingEntryList添加合同号（添加空值检查）
                    if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                        this.deliveryNoteDetails.ladingEntryList.forEach(item => {
                            if (item.XSTDMX_DDBH && !uniqueContractNumbers.has(item.XSTDMX_DDBH)) {
                                uniqueContractNumbers.add(item.XSTDMX_DDBH);
                                this.details_customerContractNumberLabelList.push({
                                    label: item.XSTDMX_DDBH,
                                    value: item.XSTDMX_DDBH
                                });
                            }
                        });
                    }
                    // 如果没有提单编号，则设置为第一个选项
                    if (!this.deliveryNoteDetails.XSTD_TDBH && this.details_customerContractNumberLabelList.length > 0) {
                        this.$set(this.deliveryNoteDetails, 'XSTD_C2', this.details_customerContractNumberLabelList[0].value);
                    }
                }
            },

            // 提单_保存提单
            details_saveDeliveryNote() {
                this.$confirm('是否保存此提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post("/sale/fahuo/saveDeliveryNote", [this.deliveryNoteDetails]).then((res) => {
                        if (res.data.flag) {
                            this.getDeliveryNoteList();
                            this.$message.success("提单保存成功");

                            // 重新获取提单详情
                            axios.post("/sale/fahuo/getDeliveryNoteDetails", this.deliveryNoteDetails).then((detailRes) => {
                                if (detailRes.data.flag) {
                                    this.deliveryNoteDetails = detailRes.data.data;
                                    this.deliveryNoteDetails.ladingEntryList = detailRes.data.data.ladingEntryList;

                                    // 更新客户选项列表（添加空值检查）
                                    this.details_soldToCustomerLabelList = this.deliveryNoteDetails.XSTD_SHDKH ? [{
                                        label: this.deliveryNoteDetails.XSTD_SHDKHMC || '',
                                        value: this.deliveryNoteDetails.XSTD_SHDKH
                                    }] : [];
                                    this.details_shippedToCustomerLabelList = this.deliveryNoteDetails.XSTD_SODKH ? [{
                                        label: this.deliveryNoteDetails.XSTD_SODKHMC || '',
                                        value: this.deliveryNoteDetails.XSTD_SODKH
                                    }] : [];
                                    this.details_invoiceToCustomerLabelList = this.deliveryNoteDetails.XSTD_SPKH ? [{
                                        label: this.deliveryNoteDetails.XSTD_SPKHMC || '',
                                        value: this.deliveryNoteDetails.XSTD_SPKH
                                    }] : [];
                                    this.details_paymentCustomerLabelList = this.deliveryNoteDetails.XSTD_FKKH ? [{
                                        label: this.deliveryNoteDetails.XSTD_FKKHMC || '',
                                        value: this.deliveryNoteDetails.XSTD_FKKH
                                    }] : [];

                                    // 构建产品类型选项集合（添加空值检查）
                                    this.details_productTypeLabelList = [];
                                    const uniqueProductNumber = new Set();
                                    // 遍历 ladingEntryList 数组
                                    if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                                        this.deliveryNoteDetails.ladingEntryList.forEach(entry => {
                                            const productNumber = entry.XSTDMX_WLBH;
                                            // 如果产品编号不在 Set 中，则添加到 details_productTypeLabelList 并更新 Set
                                            if (productNumber && !uniqueProductNumber.has(productNumber)) {
                                                uniqueProductNumber.add(productNumber);
                                                this.details_productTypeLabelList.push({
                                                    label: entry.lswlzd_wlmc || '',
                                                    value: entry.XSTDMX_WLBH,
                                                    description: entry.LSWLZD_GGXH || ''
                                                });
                                            }
                                        });
                                    }

                                    // 更新客户合同号选项集合
                                    this.details_customerContractNumberLabelList = [];
                                    const uniqueContractNumbers = new Set();

                                    // 先添加当前选中的合同号（如果存在）
                                    if (this.deliveryNoteDetails.XSTD_C2) {
                                        uniqueContractNumbers.add(this.deliveryNoteDetails.XSTD_C2);
                                        this.details_customerContractNumberLabelList = [{
                                            label: this.deliveryNoteDetails.XSTD_C2,
                                            value: this.deliveryNoteDetails.XSTD_C2
                                        }];
                                    }

                                    // 遍历ladingEntryList添加合同号（添加空值检查）
                                    if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                                        this.deliveryNoteDetails.ladingEntryList.forEach(item => {
                                            if (item.XSTDMX_DDBH && !uniqueContractNumbers.has(item.XSTDMX_DDBH)) {
                                                uniqueContractNumbers.add(item.XSTDMX_DDBH);
                                                this.details_customerContractNumberLabelList.push({
                                                    label: item.XSTDMX_DDBH,
                                                    value: item.XSTDMX_DDBH
                                                });
                                            }
                                        });
                                    }

                                    // 刷新其他相关选项列表
                                    this.details_getCustomerPhoneLabelList(true);// 客户电话选项
                                    this.details_getCustomerAddressLabelList(true);// 客户地址选项
                                    this.details_getDestinationLabelList(true);// 到货地点选项
                                    this.details_getContactLabelList(true);// 联系人选项
                                } else {
                                    this.$message.error("提单详情获取失败");
                                }
                            });
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 提单_提交提单
            details_submitDeliveryNote() {
                this.$confirm('是否提交此提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let deliveryNoteDetails = this.deliveryNoteDetails;
                    deliveryNoteDetails.XSTD_SFQR = "1";
                    axios.post("/sale/fahuo/saveDeliveryNote", [deliveryNoteDetails]).then((res) => {
                        if (res.data.flag) {
                            this.deliveryNoteDetails = {};
                            this.details_deliveryNoteDetailsVisible = false;
                            this.getDeliveryNoteList();
                            this.$message.success("提单提交成功");
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 提单_撤回提单
            details_undoSubmitDeliveryNote() {
                this.$confirm('是否撤回此提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post("/sale/fahuo/undoSubmitDeliveryNote", [this.deliveryNoteDetails]).then((res) => {
                        if (res.data.flag) {
                            this.deliveryNoteDetails = {};
                            this.details_deliveryNoteDetailsVisible = false;
                            this.getDeliveryNoteList();
                            this.$message.success("提单撤回成功");
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 提单_删除提单
            details_deleteDeliveryNote() {
                this.$confirm('是否删除此提单?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.post("/sale/fahuo/deleteDeliveryNote", [this.deliveryNoteDetails]).then((res) => {
                        if (res.data.flag) {
                            this.deliveryNoteDetails = {};
                            this.details_deliveryNoteDetailsVisible = false;
                            this.getDeliveryNoteList();
                            this.$message.success("提单删除成功");
                        } else {
                            this.$message.error("提单删除失败");
                        }
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '操作取消'
                    });
                });
            },

            // 提单_关闭提单详情
            details_closeDeliveryNote() {
                this.deliveryNoteDetails = {};
                this.details_deliveryNoteDetailsVisible = false;
                this.$message({
                    type: 'info',
                    message: '返回提单列表'
                });
            },

            /*详情分录*/
            // 提单_修改提单详情信息（数值类）
            details_updateDetailsFields(fieldName, ladingEntry, index) {
                // console.log(`修改字段: ${fieldName}, 行索引: ${index}`);
                let detailsUpdateDTO = {};
                detailsUpdateDTO.fieldName = fieldName;
                detailsUpdateDTO.deliveryNoteDetails = this.deliveryNoteDetails;
                detailsUpdateDTO.ladingEntry = ladingEntry;
                axios.post("/sale/fahuo/updateDetailsFields", detailsUpdateDTO).then((res) => {
                    if (res.data.flag) {
                        this.$set(this.deliveryNoteDetails.ladingEntryList, index, res.data.data);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                });
            },

            // 分录_添加提单分录
            details_addLadingEntry() {
                if (!this.deliveryNoteDetails.ladingEntryList) {
                    this.$set(this.deliveryNoteDetails, 'ladingEntryList', []);
                }
                this.deliveryNoteDetails.ladingEntryList.push({});
            },

            // 分录_删除提单分录
            details_deleteLadingEntry(row) {
                if (row.XSTDMX_TDFL) {
                    let ladingEntryDeleteDTO = {
                        XSTDMX_TDLS: row.XSTDMX_TDLS,
                        XSTDMX_TDFL: row.XSTDMX_TDFL
                    };
                    axios.post("/sale/fahuo/deleteLadingEntry", ladingEntryDeleteDTO).then((res) => {
                        if (res.data.flag) {
                            this.deliveryNoteDetails.ladingEntryList = this.deliveryNoteDetails.ladingEntryList.filter(item => item.XSTDMX_TDFL !== row.XSTDMX_TDFL);
                            this.$message.success("分录删除成功");
                        } else {
                            this.$message.error("分录删除失败");
                        }
                    });
                } else {
                    let rowIndex = this.deliveryNoteDetails.ladingEntryList.indexOf(row);
                    if (rowIndex > -1) {
                        this.deliveryNoteDetails.ladingEntryList.splice(rowIndex, 1);
                    }
                    this.$message.success("分录删除成功");
                }
            },

            // 分录_获取仓库名称选项集合
            details_getWarehouseLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getWarehouseLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_warehouseLabelList = res.data.data || [];
                        } else {
                            this.details_warehouseLabelList = [];
                            this.$message.error("仓库获取失败");
                        }
                    }).catch(() => {
                        this.details_warehouseLabelList = [];
                        this.$message.error("仓库获取失败");
                    });
                }
            },

            // 分录_获取产品类型选项集合
            details_getProductTypeLabelList(productType) {
                if (productType !== "") {
                    let remoteQuery = {
                        productType: productType
                    }
                    axios.post("/sale/fahuo/getProductTypeLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.details_productTypeLabelList = res.data.data || [];
                        } else {
                            this.details_productTypeLabelList = [];
                            this.$message.error("产品类型获取失败");
                        }
                    }).catch(() => {
                        this.details_productTypeLabelList = [];
                        this.$message.error("产品类型获取失败");
                    });
                }
            },

            // 分录_切换产品类型
            details_changeProductType(row) {
                const productType = this.details_productTypeLabelList.find(item => item.value === row.XSTDMX_WLBH);
                if (productType) {
                    row.lswlzd_wlmc = productType.label;
                    row.LSWLZD_GGXH = productType.description;
                    axios.post("/sale/fahuo/changeProductType", row).then((res) => {
                        if (res.data.flag) {
                            row = res.data.data;
                        } else {
                            this.$message.error("产品信息获取失败")
                        }
                    })
                } else {
                    this.$message.error("产品类型信息不存在");
                }
            },

            // 分录_切换数量
            details_changeQuantity(row) {
                const productType = this.details_productTypeLabelList.find(item => item.value === row.XSTDMX_WLBH);
                if (productType) {
                    row.lswlzd_wlmc = productType.label;
                    row.LSWLZD_GGXH = productType.description;
                    axios.post("/sale/fahuo/changeQuantity", row).then((res) => {
                        if (res.data.flag) {
                            row = res.data.data;
                        } else {
                            this.$message.error("产品信息获取失败")
                        }
                    })
                } else {
                    this.$message.error("产品类型信息不存在");
                }
            },

            // 分录_获取分录类型选项集合
            // 分录_获取库存方式选项集合
            // 分录_获取库存预留选项集合

            /*订单列表*/
            // 订单_打开订单列表弹窗
            order_openOrderListDialog(creat) {
                // 重置查询条件
                this.orderQuery = {
                    currentPage: 1,//当前页码
                    pageSize: 50,//每页显示的记录数
                    total: 0,//总记录数

                    orderInformationList: [],
                    orderNumber: "",// 订单编号
                    customerOrderNumber: "",// 对方合同号
                    customerOrderDetail: "",// 对方合同明细
                    productType: "",// 产品类型
                    customerNumberList: [],// 客户编号集合
                    departmentNumberList: [],// 部门编号集合
                    employeeNumberList: [],// 员工编号集合

                    queryDate: ["", ""],// 查询日期（无需接收）
                    startDate: "",// 起始日期
                    endDate: "",// 截止日期
                };
                // 创建新提单
                if (creat) {
                    this.deliveryNoteDetails = {};
                    // 设定默认日期
                    this.$set(this.deliveryNoteDetails, 'XSTD_DJRQ', this.util_formatDetailsDate(new Date()));
                    this.$set(this.deliveryNoteDetails, 'XSTD_YWRQ', this.util_formatDetailsDate(new Date()));
                    // 设定草稿状态（默认非草稿）
                    this.$set(this.deliveryNoteDetails, 'XSTD_SFQR', 1);
                    this.$set(this.deliveryNoteDetails, 'XSTD_SFQR_BIT', true);
                }
                // 设定客户查询条件等于提单客户
                if (this.deliveryNoteDetails.XSTD_SHDKH) {
                    this.orderQuery.customerNumberList = [this.deliveryNoteDetails.XSTD_SHDKH];
                    this.order_customerLabelList = [{
                        label: this.deliveryNoteDetails.XSTD_SHDKHMC,
                        value: this.deliveryNoteDetails.XSTD_SHDKH
                    }];
                }
                this.order_getOrderList();
                this.order_orderListVisible = true;
            },

            // 订单_获取订单列表
            order_getOrderList() {
                // 订单信息集合
                if (this.deliveryNoteDetails.ladingEntryList) {
                    this.deliveryNoteDetails.ladingEntryList.forEach(item => {
                        this.orderQuery.orderInformationList.push({
                            XSTDMX_DDLS: item.XSTDMX_DDLS,
                            XSTDMX_DDFL: item.XSTDMX_DDFL
                        });
                    });
                }
                // 交货日期
                if (this.orderQuery.queryDate == null) {
                    this.orderQuery.queryDate = ["", ""];
                }
                this.orderQuery.startDate = this.orderQuery.queryDate[0];
                this.orderQuery.endDate = this.orderQuery.queryDate[1];
                this.order_tableLoading = true;
                axios.post("/sale/fahuo/getOrderList", this.orderQuery).then((res) => {
                    if (res.data.flag) {
                        this.orderQuery.currentPage = Number(res.data.data.currentPage);
                        this.orderQuery.pageSize = Number(res.data.data.pageSize);
                        this.orderQuery.total = Number(res.data.data.total);
                        this.order_orderList = res.data.data.records || [];
                    } else {
                        this.order_orderList = [];
                        this.$message.error(res.data.msg);
                    }
                }).finally(() => {
                    this.order_tableLoading = false;
                });
            },

            // 订单_刷新订单搜索
            order_refreshOrderSearch() {
                this.orderQuery = {
                    currentPage: 1,//当前页码
                    pageSize: 10,//每页显示的记录数
                    total: 0,//总记录数

                    orderInformationList: [],
                    orderNumber: "",// 订单编号
                    customerOrderNumber: "",// 对方合同号
                    customerOrderDetail: "",// 对方合同明细
                    productType: "",// 产品类型
                    customerNumberList: [],// 客户编号集合
                    departmentNumberList: [],// 部门编号集合
                    employeeNumberList: [],// 员工编号集合

                    queryDate: ["", ""],// 查询日期（无需接收）
                    startDate: "",// 起始日期
                    endDate: "",// 截止日期
                };
                this.order_getOrderList();
            },

            // 订单_切换订单页面
            order_handleCurrentChange(currentPage) {
                this.orderQuery.currentPage = currentPage;
                this.order_getOrderList()
            },

            // 订单_获取客户选项集合
            order_getCustomerLabelList(customerName) {
                if (customerName !== "") {
                    let remoteQuery = {
                        customerName: customerName
                    }
                    axios.post("/sale/fahuo/getCustomerLabelList", remoteQuery).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.order_customerLabelList = res.data.data || [];
                        } else {
                            this.order_customerLabelList = [];
                            this.$message.error("客户获取失败");
                        }
                    }).catch(() => {
                        this.order_customerLabelList = [];
                        this.$message.error("客户获取失败");
                    });
                }
            },

            // 订单_获取列表部门选项集合
            order_getDepartmentLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getDepartmentLabelList").then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.order_departmentLabelList = (res.data.data || []).filter(item => {
                                return item && item.label && (item.label.includes('销售') || item.label.includes('贸易'));
                            });
                        } else {
                            this.order_departmentLabelList = [];
                            this.$message.error(res.data.msg || "部门选项获取失败");
                        }
                    }).catch(() => {
                        this.order_departmentLabelList = [];
                        this.$message.error("部门选项获取失败");
                    });
                }
            },

            // 订单_部门选择改变
            order_changeDepartmentSelect() {
                this.orderQuery.employeeNumberList = [];
                this.order_getEmployeeLabelList(this.orderQuery.departmentNumberList);
                this.order_getOrderList();
            },

            // 订单_获取业务员选项集合
            order_getEmployeeLabelList(visible) {
                if (visible) {
                    axios.post("/sale/fahuo/getEmployeeLabelList", this.orderQuery.departmentNumberList).then((res) => {
                        if (res.data.flag && res.data.data) {
                            this.order_employeeLabelList = res.data.data || [];
                        } else {
                            this.order_employeeLabelList = [];
                            this.$message.error(res.data.msg || "业务员选项获取失败");
                        }
                    }).catch(() => {
                        this.order_employeeLabelList = [];
                        this.$message.error("业务员选项获取失败");
                    });
                }
            },

            // 订单_参照订单
            order_referOrder(row) {
                // 校验订单客户是否与提单一致
                if (this.deliveryNoteDetails.XSTD_SHDKH && row.XSDD_SHDKH !== this.deliveryNoteDetails.XSTD_SHDKH) {
                    this.$message.warning("请选择与提单客户一致的订单");
                    return;
                }
                let orderReferDTO = {
                    orderList: [row],
                    deliveryNoteDetails: this.deliveryNoteDetails
                };
                axios.post("/sale/fahuo/referOrder", orderReferDTO).then((res) => {
                    if (res.data.flag) {
                        this.deliveryNoteDetails = res.data.data;
                        this.$set(this.deliveryNoteDetails, 'XSTD_ZLBH', "01");
                        this.$set(this.deliveryNoteDetails, 'XSYWLX_YWBH', row.XSYWLX_YWBH);
                        this.deliveryNoteDetails.ladingEntryList = res.data.data.ladingEntryList;
                        // 构建客户选项列表（添加空值检查）
                        this.details_soldToCustomerLabelList = this.deliveryNoteDetails.XSTD_SHDKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SHDKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SHDKH
                        }] : [];
                        this.details_shippedToCustomerLabelList = this.deliveryNoteDetails.XSTD_SODKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SODKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SODKH
                        }] : [];
                        this.details_invoiceToCustomerLabelList = this.deliveryNoteDetails.XSTD_SPKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SPKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SPKH
                        }] : [];
                        this.details_paymentCustomerLabelList = this.deliveryNoteDetails.XSTD_FKKH ? [{
                            label: this.deliveryNoteDetails.XSTD_FKKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_FKKH
                        }] : [];
                        // 构建产品类型选项集合（添加空值检查）
                        this.details_productTypeLabelList = [];
                        const uniqueProductNumber = new Set();
                        // 遍历 ladingEntryList 数组
                        if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                            this.deliveryNoteDetails.ladingEntryList.forEach(entry => {
                                const productNumber = entry.XSTDMX_WLBH;
                                // 如果产品编号不在 Set 中，则添加到 details_productTypeLabelList 并更新 Set
                                if (productNumber && !uniqueProductNumber.has(productNumber)) {
                                    uniqueProductNumber.add(productNumber);
                                    this.details_productTypeLabelList.push({
                                        label: entry.lswlzd_wlmc || '',
                                        value: entry.XSTDMX_WLBH,
                                        description: entry.LSWLZD_GGXH || ''
                                    });
                                }
                            });
                        }
                        // 提单详情选项请求
                        this.details_getDeliveryNoteTypeLabelList(true);// 提单类型选项
                        this.details_getCurrencyTypeLabelList(true);// 币种选项
                        this.details_getDepartmentLabelList(true);// 部门选项
                        this.details_getEmployeeLabelList(true);// 业务员选项
                        this.details_getBusinessTypeLabelList(true);// 业务类型选项
                        this.details_getDiscountPolicyLabelList(true);// 折扣政策选项
                        this.details_getCustomerPhoneLabelList(true);// 客户电话选项
                        this.details_getCustomerAddressLabelList(true);// 客户地址选项
                        this.details_getDestinationLabelList(true);// 到货地点选项
                        this.details_getContactLabelList(true);// 联系人选项
                        this.details_getTransportationLabelList(true);// 运输方式选项
                        this.details_getCustomerContractNumberLabelList(true);// 客户合同编号选项
                        // 提单分录选项请求
                        this.details_getWarehouseLabelList(true);
                        this.order_orderListVisible = false;
                        this.details_deliveryNoteDetailsVisible = true;
                    } else {
                        this.$message.error("订单参照失败");
                    }
                });
            },

            // 订单_列表选择改变
            order_handleSelectionChange(selection) {
                // 保存选中项
                this.order_selectedOrderList = selection;

                // 如果存在旧通知,先关闭它
                if (this.order_currentNotification) {
                    this.order_currentNotification.close();
                }

                if (selection.length > 0) {
                    // 按币种分组统计
                    const stats = selection.reduce((acc, curr) => {
                        const currency = curr.LSWBZD_BM || '未知币种';
                        if (!acc[currency]) {
                            acc[currency] = {
                                quantity: 0,
                                amount: 0
                            };
                        }
                        acc[currency].quantity += Number(curr.wf_sl || 0);
                        acc[currency].amount += Number(curr.XSDDMX_YXSE || 0);
                        return acc;
                    }, {});

                    // 构建通知消息
                    let message = Object.entries(stats).map(([currency, { quantity, amount }]) => {
                        return `${currency}:<br>未发数量: ${quantity.toFixed(2)}<br>销售金额: ${amount.toFixed(2)}`;
                    }).join('<br>');


                    // 显示新通知并保存引用
                    this.order_currentNotification = this.$notify({
                        title: '已选择订单统计',
                        message: message,
                        type: 'warning',
                        position: 'top-right',
                        duration: 10000,
                        dangerouslyUseHTMLString: true,
                        customClass: 'wide-notification',  // 添加自定义类名
                        onClose: () => {
                            // 通知关闭时清除引用
                            this.currentNotification = null;
                        }
                    });
                }
            },

            // 订单_批量参照订单
            order_batchReferOrder() {
                // 校验是否选择订单
                if (this.order_selectedOrderList.length === 0) {
                    this.$message.warning("请选择订单");
                    return;
                }
                // 校验所选择的订单是否为相同客户
                const customerNumber = this.order_selectedOrderList[0]?.XSDD_SHDKH;
                const allMatch = this.order_selectedOrderList.every(item => item.XSDD_SHDKH === customerNumber);
                if (!allMatch) {
                    this.$message.warning("请选择相同客户的订单");
                    return;
                }
                // 校验订单客户是否与提单一致
                if (this.deliveryNoteDetails.XSTD_SHDKH && this.order_selectedOrderList[0]?.XSDD_SHDKH !== this.deliveryNoteDetails.XSTD_SHDKH) {
                    this.$message.warning("请选择与提单客户一致的订单");
                    return;
                }
                // 发送请求
                let orderReferDTO = {
                    orderList: this.order_selectedOrderList,
                    deliveryNoteDetails: this.deliveryNoteDetails
                }
                axios.post("/sale/fahuo/referOrder", orderReferDTO).then((res) => {
                    if (res.data.flag) {
                        this.deliveryNoteDetails = res.data.data;
                        this.$set(this.deliveryNoteDetails, 'XSTD_ZLBH', "01");
                        this.$set(this.deliveryNoteDetails, 'XSYWLX_YWBH', this.order_selectedOrderList[0].XSYWLX_YWBH);
                        this.deliveryNoteDetails.ladingEntryList = res.data.data.ladingEntryList;
                        // 构建客户选项列表（添加空值检查）
                        this.details_soldToCustomerLabelList = this.deliveryNoteDetails.XSTD_SHDKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SHDKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SHDKH
                        }] : [];
                        this.details_shippedToCustomerLabelList = this.deliveryNoteDetails.XSTD_SODKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SODKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SODKH
                        }] : [];
                        this.details_invoiceToCustomerLabelList = this.deliveryNoteDetails.XSTD_SPKH ? [{
                            label: this.deliveryNoteDetails.XSTD_SPKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_SPKH
                        }] : [];
                        this.details_paymentCustomerLabelList = this.deliveryNoteDetails.XSTD_FKKH ? [{
                            label: this.deliveryNoteDetails.XSTD_FKKHMC || '',
                            value: this.deliveryNoteDetails.XSTD_FKKH
                        }] : [];
                        // 构建产品类型选项集合（添加空值检查）
                        this.details_productTypeLabelList = [];
                        const uniqueProductNumber = new Set();
                        // 遍历 ladingEntryList 数组
                        if (this.deliveryNoteDetails.ladingEntryList && this.deliveryNoteDetails.ladingEntryList.length > 0) {
                            this.deliveryNoteDetails.ladingEntryList.forEach(entry => {
                                const productNumber = entry.XSTDMX_WLBH;
                                // 如果产品编号不在 Set 中，则添加到 details_productTypeLabelList 并更新 Set
                                if (productNumber && !uniqueProductNumber.has(productNumber)) {
                                    uniqueProductNumber.add(productNumber);
                                    this.details_productTypeLabelList.push({
                                        label: entry.lswlzd_wlmc || '',
                                        value: entry.XSTDMX_WLBH,
                                        description: entry.LSWLZD_GGXH || ''
                                    });
                                }
                            });
                        }
                        // 提单详情选项请求
                        this.details_getDeliveryNoteTypeLabelList(true);// 提单类型选项
                        this.details_getCurrencyTypeLabelList(true);// 币种选项
                        this.details_getDepartmentLabelList(true);// 部门选项
                        this.details_getEmployeeLabelList(true);// 业务员选项
                        this.details_getBusinessTypeLabelList(true);// 业务类型选项
                        this.details_getDiscountPolicyLabelList(true);// 折扣政策选项
                        this.details_getCustomerPhoneLabelList(true);// 客户电话选项
                        this.details_getCustomerAddressLabelList(true);// 客户地址选项
                        this.details_getDestinationLabelList(true);// 到货地点选项
                        this.details_getContactLabelList(true);// 联系人选项
                        this.details_getTransportationLabelList(true);// 运输方式选项
                        this.details_getCustomerContractNumberLabelList(true);// 客户合同编号选项

                        // 提单分录选项请求
                        this.details_getWarehouseLabelList(true);
                        this.order_orderListVisible = false;
                        this.details_deliveryNoteDetailsVisible = true;
                    } else {
                        this.$message.error("订单参照失败");
                    }
                });
            },

            /*其他*/
            // 日期字符串转换
            formatDate(dateStr) {
                if (dateStr && dateStr.trim() !== '') {
                    const year = dateStr.substring(0, 4);
                    const month = dateStr.substring(4, 6);
                    const day = dateStr.substring(6, 8);
                    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                }
            },

            // 提单详情日期格式化
            util_formatDetailsDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');

                return `${year}${month}${day}`;
            }
        }
    })
</script>

</html>